from fastapi import <PERSON><PERSON><PERSON>, HTTPException, APIRouter
from pydantic import BaseModel
from instagrapi import Client
from instagrapi.exceptions import Lo<PERSON><PERSON>equired, ClientError
from telegram import Bot
import logging
import sqlite3
import re
from urllib.parse import urlparse
import os
from environs import Env
from youtube_downloader import YouTubeDownloader
import telebot
from send_video import send_video

from login import update_session


logging.basicConfig(format='%(asctime)s - %(name)s - %(levelname)s - %(message)s', level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI()
router = APIRouter(prefix="/api")

class DownloadRequest(BaseModel):
    chat_id: int
    url: str
    bot_token: str

class VerificationRequest(BaseModel):
    chat_id: int
    code: str
    bot_token: str

class InstagramClient:
    def __init__(self):
        self.client = Client()
        self.client.delay_range = [1, 3]
        self.client.set_device({
            "manufacturer": "Apple",
            "model": "iPhone12,1",
            "android_version": 29,
            "android_release": "10"
        })
        self.logged_in = False
        env = Env()
        env.read_env()
        self.username = env.str("INSTAGRAM_USERNAME", default="muhammadali_abc")
        self.password = env.str("INSTAGRAM_PASSWORD", default="muhammadalI1")
        self.session_file = "session.json"
        self.challenge_context = None

    async def ensure_login(self):
        update_session()

    async def download_media(self, url: str, chat_id: int, bot: Bot, db_conn: sqlite3.Connection) -> None:
        if not is_valid_instagram_url(url):
            await bot.send_message(chat_id=chat_id, text="Invalid Instagram link. Please send a valid post URL.")
            return

        try:
            await self.ensure_login()
            shortcode = urlparse(url).path.split('/')[-2]
            
            try:
                media = self.client.media_info(self.client.media_pk_from_code(shortcode))
            except Exception as e:
                if "login_required" in str(e) or "401" in str(e) or "403" in str(e):
                    logger.warning("Session expired during media download, attempting to update session")
                    try:
                        from login import update_session
                        if update_session():
                            self.client.load_settings(self.session_file)
                            self.client.login(self.username, self.password)
                            self.logged_in = True
                            media = self.client.media_info(self.client.media_pk_from_code(shortcode))
                        else:
                            raise Exception("Failed to update session")
                    except Exception as update_error:
                        logger.error(f"Failed to update session during media download: {update_error}")
                        raise
                else:
                    raise

            c = db_conn.cursor()

            if media.caption_text:
                await bot.send_message(chat_id=chat_id, text=f"Caption: {media.caption_text}")

            if media.media_type == 1:
                msg = await bot.send_photo(chat_id=chat_id, photo=str(media.thumbnail_url))
                if msg and msg.photo:
                    file_id = msg.photo[-1].file_id
                    c.execute("INSERT INTO history (user_id, url, file_id, timestamp) VALUES (?, ?, ?, datetime('now'))", (chat_id, url, file_id))
            elif media.media_type == 2:
                msg = await bot.send_video(chat_id=chat_id, video=str(media.video_url))
                if msg and msg.video:
                    file_id = msg.video.file_id
                    c.execute("INSERT INTO history (user_id, url, file_id, timestamp) VALUES (?, ?, ?, datetime('now'))", (chat_id, url, file_id))
            elif media.media_type == 8:
                for resource in media.resources:
                    if resource.media_type == 1:
                        msg = await bot.send_photo(chat_id=chat_id, photo=str(resource.thumbnail_url))
                        if msg and msg.photo:
                            file_id = msg.photo[-1].file_id
                            c.execute("INSERT INTO history (user_id, url, file_id, timestamp) VALUES (?, ?, ?, datetime('now'))", (chat_id, url, file_id))
                    elif resource.media_type == 2:
                        msg = await bot.send_video(chat_id=chat_id, video=str(resource.video_url))
                        if msg and msg.video:
                            file_id = msg.video.file_id
                            c.execute("INSERT INTO history (user_id, url, file_id, timestamp) VALUES (?, ?, ?, datetime('now'))", (chat_id, url, file_id))
            else:
                await bot.send_message(chat_id=chat_id, text="No media found in the post.")

            db_conn.commit()

        except LoginRequired:
            await bot.send_message(chat_id=chat_id, text="Private account or restricted access. Please log in.")
        except ClientError as e:
            logger.error(f"ClientError: {e}")
            await bot.send_message(chat_id=chat_id, text="This is a private account or access is restricted.")
        except Exception as e:
            logger.error(f"Exception: {e}")
            await bot.send_message(chat_id=chat_id, text="No media found in the post.")

def init_db():
    conn = sqlite3.connect('sqlite3.db')
    c = conn.cursor()
    c.execute('''CREATE TABLE IF NOT EXISTS history 
                 (user_id INTEGER, url TEXT, file_id TEXT, timestamp TEXT)''')
    conn.commit()
    return conn

db_conn = init_db()
instagram_client = InstagramClient()
youtube_client = YouTubeDownloader()

def is_valid_instagram_url(url):
    pattern = r'https?://(www\.)?instagram\.com/(p|reel|tv|s|share)/[A-Za-z0-9_-]+/?(\?.*)?$'
    return bool(re.match(pattern, url))

def is_valid_youtube_url(url):
    pattern = r'(?:https?://)?(?:www\.)?(?:youtube\.com/(?:watch\?v=|embed/|v/|shorts/)|youtu\.be/)([^"&?/ ]{11})'
    return re.match(pattern, url)

@router.post("/download")
async def download_media(request: DownloadRequest):
    try:
        bot = Bot(token=request.bot_token)
        telebot_instance = telebot.TeleBot(token=request.bot_token)
        
        if is_valid_instagram_url(request.url):
            await instagram_client.download_media(request.url, request.chat_id, bot, db_conn)
            return {"status": "success", "message": "Instagram media sent to Telegram chat."}
        elif is_valid_youtube_url(request.url):
            result = youtube_client.download_video(request.url)
            if result["success"]:
                try:
                    msg = send_video(
                        bot=telebot_instance,
                        chat_id=request.chat_id,
                        video_path=result["file_path"],
                        caption=result["title"],
                        duration=result.get("duration"),
                        width=result.get("width"),
                        height=result.get("height"),
                        thumbnail_path=result.get("thumbnail_path")
                    )
                    
                    if msg and msg.video:
                        c = db_conn.cursor()
                        c.execute(
                            "INSERT INTO history (user_id, url, file_id, timestamp) VALUES (?, ?, ?, datetime('now'))",
                            (request.chat_id, request.url, msg.video.file_id)
                        )
                        db_conn.commit()

                    # Clean up files
                    os.remove(result["file_path"])
                    if result.get("thumbnail_path") and os.path.exists(result["thumbnail_path"]):
                        os.remove(result["thumbnail_path"])
                    return {"status": "success", "message": "YouTube video sent to Telegram chat."}
                except Exception as e:
                    logger.error(f"Error sending video: {e}")
                    await bot.send_message(chat_id=request.chat_id, text=f"Error sending video: {str(e)}")
                    return {"status": "error", "message": f"Error sending video: {str(e)}"}
            else:
                await bot.send_message(chat_id=request.chat_id, text=f"Error downloading YouTube video: {result['message']}")
                return {"status": "error", "message": result["message"]}
        else:
            await bot.send_message(chat_id=request.chat_id, text="Invalid URL. Please send a valid Instagram or YouTube URL.")
            return {"status": "error", "message": "Invalid URL"}
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"Error processing request: {e}")
        raise HTTPException(status_code=500, detail="Failed to process request.")

app.include_router(router)

@app.on_event("startup")
async def startup_event():
    pass

@app.on_event("shutdown")
async def shutdown_event():
    pass

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=3333)
