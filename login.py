from instagrapi import Client
import imaplib
import email
import re
import time
from environs import Env

env = Env()
env.read_env()

EMAIL = "<EMAIL>"
EMAIL_PASSWORD = "drilpapnlsjvdyxi"
IMAP_SERVER = "imap.gmail.com"

IG_USERNAME = "milliy_fasonlar_olami_1"
IG_PASSWORD = "2V2Y82QUY"

SESSION_PATH = env.str("SESSION_PATH", default="session.json")


def get_latest_ig_code(timeout=60, interval=5):
    """
    Instagramdan yuborilgan emaildan so'nggi 6 xonali kodni olib keladi.
    """
    print("📨 Emaildan kod olinmoqda...")
    end_time = time.time() + timeout
    while time.time() < end_time:
        try:
            mail = imaplib.IMAP4_SSL(IMAP_SERVER)
            mail.login(EMAIL, EMAIL_PASSWORD)
            mail.select("inbox")

            result, data = mail.search(None, 'FROM "<EMAIL>"')
            email_ids = data[0].split()
            if not email_ids:
                time.sleep(interval)
                continue

            latest_email_id = email_ids[-1]
            result, data = mail.fetch(latest_email_id, "(RFC822)")
            raw_email = data[0][1]
            msg = email.message_from_bytes(raw_email)

            body = ""
            if msg.is_multipart():
                for part in msg.walk():
                    if part.get_content_type() == "text/plain":
                        body += part.get_payload(decode=True).decode()
            else:
                body = msg.get_payload(decode=True).decode()

            match = re.search(r"(\d{6})", body)
            if match:
                return match.group(1)
        except Exception as e:
            print("❌ Xatolik:", e)

        time.sleep(interval)
    raise TimeoutError("⏰ Emaildan kod topilmadi (timeout).")

def challenge_code_handler(username, choice):
    print(f"📩 Instagram challenge code uchun email kuzatilmoqda...")
    code = get_latest_ig_code()
    print("✅ Kod topildi:", code)
    return code

def update_session(max_retries=2):
    def retry_login():
        for attempt in range(1, max_retries + 1):
            try:
                cl = Client()
                print(f"🔁 Login urinish #{attempt}")
                cl.challenge_code_handler = challenge_code_handler
                cl.login(IG_USERNAME, IG_PASSWORD)
                cl.dump_settings("session.json")
                print("🎉 Instagramga qayta muvaffaqiyatli kirdingiz!")
                return cl
            except Exception as e:
                print(f"❌ Urinish #{attempt} xatolik: {e}")
                if "Please check the code" not in str(e) or attempt == max_retries:
                    raise e
                time.sleep(10)

    try:
        cl = Client()
        cl.load_settings("session.json")
        cl.get_timeline_feed()
        print("✅ Mavjud session ishladi.")
        return cl
    except Exception as e:
        print(f"⚠️ Session yaroqsiz yoki login kerak: {e}")
        try:
            return retry_login()
        except Exception as e:
            print(f"❌ Sessiyani yangilashda yakuniy xatolik: {e}")
            return None


update_session()
