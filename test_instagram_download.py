import sqlite3
import asyncio
from app import InstagramClient
from telegram import Bo<PERSON>
from environs import Env

env = Env()
env.read_env()


def init_db():
    conn = sqlite3.connect('sqlite3.db')
    c = conn.cursor()
    c.execute('''CREATE TABLE IF NOT EXISTS history 
                (user_id INTEGER, url TEXT, file_id TEXT, timestamp TEXT)''')
    conn.commit()
    return conn


async def test_download():
    bot_token = "8189801916:AAGxfMWHbBIDJ-NI7HmuFbDz6s7CCOxef9k"
    chat_id = 2105729169

    instagram_client = InstagramClient()
    bot = Bot(token=bot_token)

    # Test URL
    url = "https://www.instagram.com/reel/DKXe7rSNJVZ/?igsh=c3NuYTZ2N2M1bDF3"
    try:
        db_conn = init_db()
        await instagram_client.download_media(url, chat_id, bot, db_conn)

    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    asyncio.run(test_download())
