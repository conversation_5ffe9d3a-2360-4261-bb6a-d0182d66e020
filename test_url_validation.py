import unittest
from app import is_valid_instagram_url

class TestInstagramURLValidation(unittest.TestCase):
    def test_valid_instagram_urls(self):
        valid_urls = [
            "https://www.instagram.com/p/ABC123/",
            "https://www.instagram.com/reel/ABC123/",
            "https://www.instagram.com/tv/ABC123/",
            "https://www.instagram.com/s/ABC123/",
            "https://www.instagram.com/share/ABC123/",
            "https://www.instagram.com/p/ABC123/?igshid=xyz",
            "https://www.instagram.com/share/_m6xEluB-?fhid=X202eEVsdUIt",
            "https://instagram.com/p/ABC123/",
            "http://www.instagram.com/p/ABC123/"
        ]
        
        for url in valid_urls:
            with self.subTest(url=url):
                self.assertTrue(is_valid_instagram_url(url), f"URL should be valid: {url}")

    def test_invalid_instagram_urls(self):
        invalid_urls = [
            "https://www.instagram.com/",
            "https://www.instagram.com/user/",
            "https://www.instagram.com/p/",
            "https://www.instagram.com/p/ABC123/extra/",
            "https://www.facebook.com/p/ABC123/",
            "https://instagram.com/",
            "not a url",
            "https://www.instagram.com/p/",
            "https://www.instagram.com/share/",
            "https://www.instagram.com/p/ABC123/extra/path/"
        ]
        
        for url in invalid_urls:
            with self.subTest(url=url):
                self.assertFalse(is_valid_instagram_url(url), f"URL should be invalid: {url}")

if __name__ == '__main__':
    unittest.main() 